<template>
    <el-card class="page-container" shadow="hover">
        <template #header>
            <div class="breadcrumb-container">
                <el-breadcrumb separator="/">
                    <el-breadcrumb-item :to="{ path: '/' }">首页</el-breadcrumb-item>
                    <el-breadcrumb-item>
                        <a href="/Menu">RBAC</a>
                    </el-breadcrumb-item>
                    <el-breadcrumb-item>权限管理</el-breadcrumb-item>
                </el-breadcrumb>
            </div>
        </template>

        <el-card class="search-card" shadow="never">
            <el-form :inline="true">
                <el-form-item>
                    <el-input v-model="getpermission.PermissionName" prefix-icon="Search" style="width: 220px;"
                        placeholder="请输入权限名称"></el-input>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" :icon="Search" @click="gettableData()">查询</el-button>
                </el-form-item>
                <el-form-item>
                    <el-button type="success" :icon="Plus" @click="dialogVisible = true">新增权限</el-button>
                </el-form-item>
            </el-form>
        </el-card>

        <el-card class="table-card" shadow="never">
            <el-table :data="tableData" style="width: 100%" border stripe highlight-current-row
                v-loading="tableLoading">
                <el-table-column type="index" label="序号" width="80" align="center" />
                <el-table-column prop="id" label="权限编号" width="100" align="center" />
                <el-table-column prop="permissionName" label="权限名称" width="150" />
                <el-table-column prop="permissionURL" label="权限URL" min-width="180" />
                <el-table-column fixed="right" label="操作" width="150" align="center">
                    <template #default="scope">
                        <el-button type="primary" size="small" :icon="Edit" circle @click="toUpdate(scope.row)" />
                        <el-button type="danger" size="small" :icon="Delete" circle
                            @click="handleClick(scope.row.id)" />
                    </template>
                </el-table-column>
            </el-table>

            <div class="pagination-container">
                <el-pagination v-model:current-page="Page.PageIndex" v-model:page-size="Page.PageSize"
                    :page-sizes="[5, 10, 20, 50]" :background="true" layout="total, sizes, prev, pager, next, jumper"
                    :total="Page.totalCount" />
            </div>
        </el-card>

        <!-- 新增权限对话框 -->
        <el-dialog v-model="dialogVisible" title="新增权限" width="500" destroy-on-close center>
            <el-form :model="form" label-position="top" style="max-width: 460px; margin: 0 auto">
                <el-form-item label="权限名称" required>
                    <el-input v-model="form.permissionName" placeholder="请输入权限名称" />
                </el-form-item>
                <el-form-item label="权限URL" required>
                    <el-input v-model="form.permissionURL" placeholder="请输入权限URL" />
                </el-form-item>
                <el-form-item label="上级权限">
                    <el-input v-model="form.parentId" placeholder="请输入上级权限ID" />
                </el-form-item>
                <el-form-item>
                    <div class="dialog-footer">
                        <el-button @click="dialogVisible = false">取消</el-button>
                        <el-button type="primary" @click="onSubmit" :loading="submitLoading">添加权限</el-button>
                    </div>
                </el-form-item>
            </el-form>
        </el-dialog>

        <!-- 编辑权限对话框 -->
        <el-dialog v-model="UpdatedialogVisible" title="编辑权限" width="500" destroy-on-close center>
            <el-form :model="Updateform" label-position="top" style="max-width: 460px; margin: 0 auto">
                <el-form-item label="权限名称" required>
                    <el-input v-model="Updateform.permissionName" placeholder="请输入权限名称" />
                </el-form-item>
                <el-form-item label="权限URL" required>
                    <el-input v-model="Updateform.permissionURL" placeholder="请输入权限URL" />
                </el-form-item>
                <el-form-item label="上级权限">
                    <el-input v-model="Updateform.parentId" placeholder="请输入上级权限ID" />
                </el-form-item>
                <el-form-item>
                    <div class="dialog-footer">
                        <el-button @click="UpdatedialogVisible = false">取消</el-button>
                        <el-button type="primary" @click="Update" :loading="updateLoading">保存修改</el-button>
                    </div>
                </el-form-item>
            </el-form>
        </el-dialog>
    </el-card>
</template>

<script lang="ts" setup>
import axios from 'axios'
import { ElMessage, ElMessageBox } from 'element-plus'
import { onMounted, reactive, ref, watch } from 'vue'
import { Search, Plus, Edit, Delete } from '@element-plus/icons-vue'

onMounted(() => {
    axios.defaults.headers.common["Authorization"] = "Bearer " + window.localStorage.getItem("token");
    gettableData()
})

// 加载状态
const tableLoading = ref(false);
const submitLoading = ref(false);
const updateLoading = ref(false);

const dialogVisible = ref(false)
const form = reactive({
    "permissionName": "",
    "permissionURL": "",
    "parentId": 0
})


const onSubmit = () => {
    submitLoading.value = true;
    axios({
        url: 'http://localhost:5165/api/RBAC/CreatePermission',
        method: 'post',
        data: form
    })
        .then(res => {
            if (res.data.code == 200) {
                ElMessage.success(res.data.msg);
                gettableData();
                dialogVisible.value = false;
            }
            else {
                ElMessage.error(res.data.msg);
            }
        })
        .finally(() => {
            submitLoading.value = false;
        });
}

const handleClick = (id: any) => {
    ElMessageBox.confirm(
        '确定要删除该权限吗？此操作不可恢复。',
        '删除确认',
        {
            confirmButtonText: '确定删除',
            cancelButtonText: '取消',
            type: 'warning',
            draggable: true,
        }
    )
        .then(() => {
            tableLoading.value = true;
            axios({
                url: 'http://localhost:5165/api/RBAC/DeletePermission',
                method: 'delete',
                data: {
                    id: id
                }
            })
                .then(res => {
                    if (res.data.code == 200) {
                        ElMessage.success(res.data.msg);
                        gettableData();
                    }
                    else {
                        ElMessage.error(res.data.msg);
                    }
                })
                .finally(() => {
                    tableLoading.value = false;
                });
        })
        .catch(() => {
            ElMessage({
                type: 'info',
                message: '已取消删除操作',
            })
        })
}

const UpdatedialogVisible = ref(false);
const Updateform = reactive({
    "id": '',
    "permissionName": "",
    "permissionURL": "",
    "parentId": 0
})

const toUpdate = (row: any) => {
    UpdatedialogVisible.value = true;
    Updateform.id = row.id;
    Updateform.parentId = row.parentId;
    Updateform.permissionName = row.permissionName;
    Updateform.permissionURL = row.permissionURL;
}

const Update = () => {
    updateLoading.value = true;
    axios({
        url: 'http://localhost:5165/api/RBAC/UpdatePermission',
        method: 'put',
        data: Updateform
    })
        .then(res => {
            if (res.data.code == 200) {
                ElMessage.success(res.data.msg);
                gettableData();
                UpdatedialogVisible.value = false;
            }
            else {
                ElMessage.error(res.data.msg);
            }
        })
        .finally(() => {
            updateLoading.value = false;
        });
}

const tableData = ref([])
const Page = reactive({
    PageIndex: 1,
    PageSize: 5,
    totalCount: 0
})

const getpermission = reactive({
    PermissionName: ''
})

const gettableData = () => {
    tableLoading.value = true;
    axios({
        url: 'http://localhost:5043/api/RBAC/GetPermissionList',
        method: 'get',
        params: {
            PageIndex: Page.PageIndex,
            PageSize: Page.PageSize,
            PermissionName: getpermission.PermissionName
        }
    })
        .then(res => {
            if (res.data.code == 200) {
                tableData.value = res.data.pageDatas;
                Page.totalCount = res.data.totalCount;
            }
        })
        .finally(() => {
            tableLoading.value = false;
        });
}

watch(Page, () => {
    gettableData();
})
</script>
