// API接口路径统一管理
const API = {
  // 用户相关接口(使用service1 - 5043端口)
  User: {
    login: '/RBAC/Login',
    getUserList: '/RBAC/GetUserList',
    refreshToken: '/RBAC/RefreshToken'
  },
  
  // 角色相关接口(使用service1 - 5043端口)
  Role: {
    getRoleList: '/RBAC/GetRoleList'
  },
  
  // 权限相关接口(使用service1 - 5043端口)
  Permission: {
    getPermissionList: '/RBAC/GetPermissionList'
  },

  // 用户管理接口(使用service2 - 5165端口)
  UserManage: {
    createUser: '/RBAC/CreateUser',
    updateUser: '/RBAC/UpdateUser',
    deleteUser: '/RBAC/DeleteUser'
  },
  
  // 角色管理接口(使用service2 - 5165端口)
  RoleManage: {
    createRole: '/RBAC/CreateRole',
    updateRole: '/RBAC/UpdateRole',
    deleteRole: '/RBAC/DeleteRole'
  },
  
  // 权限管理接口(使用service2 - 5165端口)
  PermissionManage: {
    createPermission: '/RBAC/CreatePermission',
    updatePermission: '/RBAC/UpdatePermission',
    deletePermission: '/RBAC/DeletePermission'
  }
};

export default API; 