import { service1, service2 } from '../request';
import API from '../index';

// 用户登录(使用service1 - 5043端口)
export function login(params: {
  UserName: string;
  Password: string;
}) {
  return service1({
    url: API.User.login,
    method: 'get',
    params
  });
}

// 刷新Token(使用service1 - 5043端口)
export function refreshToken() {
  return service1({
    url: API.User.refreshToken,
    method: 'get'
  });
}

// 获取用户列表(使用service1 - 5043端口)
export function getUserList(params?: {
  PageIndex?: number;
  PageSize?: number;
  UserName?: string;
}) {
  return service1({
    url: API.User.getUserList,
    method: 'get',
    params
  });
}

// 创建用户(使用service2 - 5165端口)
export function createUser(data: {
  userName: string;
  password: string;
  isEnable: boolean;
  roleId: number[];
}) {
  return service2({
    url: API.UserManage.createUser,
    method: 'post',
    data
  });
}

// 更新用户(使用service2 - 5165端口)
export function updateUser(data: {
  id: number | string;
  userName: string;
  password?: string;
  isEnable: boolean;
  roleId: number[];
}) {
  return service2({
    url: API.UserManage.updateUser,
    method: 'put',
    data
  });
}

// 删除用户(使用service2 - 5165端口)
export function deleteUser(data: {
  id: number | string;
}) {
  return service2({
    url: API.UserManage.deleteUser,
    method: 'delete',
    data
  });
} 