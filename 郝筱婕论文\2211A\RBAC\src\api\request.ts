import axios from 'axios';
import type { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import router from '@/router';
import { ElMessage } from 'element-plus';

// 是否正在刷新token
let isRefreshing = false;
// 重试队列
let requests: Function[] = [];

// 第一个API路径的axios实例
const service1: AxiosInstance = axios.create({
  baseURL: 'http://localhost:5043/api', // 第一个API前缀
  timeout: 10000, // 请求超时时间
});

// 第二个API路径的axios实例
const service2: AxiosInstance = axios.create({
  baseURL: 'http://localhost:5165/api', // 第二个API前缀
  timeout: 10000, // 请求超时时间
});

// 添加请求拦截器
const setupInterceptors = (service: AxiosInstance) => {
  service.interceptors.request.use(
    (config) => {
      // 在发送请求之前做些什么，比如添加token
      const token = localStorage.getItem('token');
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
      return config;
    },
    (error) => {
      // 对请求错误做些什么
      return Promise.reject(error);
    }
  );

  // 响应拦截器
  service.interceptors.response.use(
    (response: AxiosResponse) => {
      // 对响应数据做点什么
      const res = response.data;
      return res;
    },
    (error) => {
      // Token过期处理
      if (error.response && error.response.status === 401) {
        const config = error.config;
        if (!isRefreshing) {
          isRefreshing = true;
          
          // 尝试刷新Token，直接使用service1发送请求
          return service1({
            url: '/RBAC/RefreshToken',
            method: 'get'
          })
            .then((res: any) => {
              if (res.code === 200) {
                // 更新本地存储中的Token
                localStorage.setItem('token', res.data.token);
                
                // 重新发送队列中的请求
                requests.forEach(cb => cb(res.data.token));
                // 清空队列
                requests = [];
                
                // 重试当前请求
                config.headers.Authorization = `Bearer ${res.data.token}`;
                return axios(config);
              } else {
                // Token刷新失败，需要重新登录
                ElMessage.error('登录已过期，请重新登录');
                localStorage.clear();
                router.push('/');
                return Promise.reject(error);
              }
            })
            .catch(() => {
              // 刷新Token失败
              ElMessage.error('登录已过期，请重新登录');
              localStorage.clear();
              router.push('/');
              return Promise.reject(error);
            })
            .finally(() => {
              isRefreshing = false;
            });
        } else {
          // 将请求加入队列
          return new Promise((resolve) => {
            requests.push((token: string) => {
              config.headers.Authorization = `Bearer ${token}`;
              resolve(axios(config));
            });
          });
        }
      }
      
      // 对响应错误做点什么
      return Promise.reject(error);
    }
  );
};

// 设置拦截器
setupInterceptors(service1);
setupInterceptors(service2);

export { service1, service2 }; 