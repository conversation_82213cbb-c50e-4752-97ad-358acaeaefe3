// API基础路径
export const API_BASE = {
  // 第一个API路径
  BASE_URL_1: 'http://localhost:5043/api',
  
  // 第二个API路径
  BASE_URL_2: 'http://localhost:5165/api'
};

// 请求超时时间
export const TIMEOUT = 10000;

// 当前环境
const ENV = process.env.NODE_ENV || 'development';

// 根据环境选择基础URL
export function getBaseUrl(): string {
  switch (ENV) {
    case 'development':
      return API_BASE.BASE_URL_1;
    case 'test':
      return API_BASE.BASE_URL_2;
    case 'production':
      return API_BASE.BASE_URL_2;
    default:
      return API_BASE.BASE_URL_1;
  }
}

// 导出当前环境
export const CURRENT_ENV = ENV; 
declare const process: {
  env: {
    NODE_ENV: 'development' | 'production' | 'test';
  };
};
import { defineConfig } from 'vite';
import vue from '@vitejs/plugin-vue';

export default defineConfig({
  plugins: [vue()] // 必须有 Vue 插件
});