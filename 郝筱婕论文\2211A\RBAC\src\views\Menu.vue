<template>
    <div class="common-layout">
        <el-container class="main-container">
            <el-header class="app-header">
                <div class="header-title">RBAC 用户角色权限管理</div>
                <div class="user-info">
                    <el-dropdown trigger="click">
                        <div class="user-dropdown-link">
                            <el-avatar :size="32" icon="User" class="user-avatar" />
                            <span class="username">{{ userName }}</span>
                            <el-icon><arrow-down /></el-icon>
                        </div>
                        <template #dropdown>
                            <el-dropdown-menu>
                                <el-dropdown-item>
                                    <el-icon>
                                        <user />
                                    </el-icon>
                                    角色：{{ roleName }}
                                </el-dropdown-item>
                                <el-dropdown-item divided @click="logout">
                                    <el-icon><switch-button /></el-icon>
                                    退出登录
                                </el-dropdown-item>
                            </el-dropdown-menu>
                        </template>
                    </el-dropdown>
                </div>
            </el-header>
            <el-container>
                <el-aside width="200px">
                    <el-menu active-text-color="#409EFF" background-color="#f5f7fa" class="el-menu-vertical-demo"
                        default-active="2" text-color="#2c3e50" @open="handleOpen" @close="handleClose" router>
                        <el-sub-menu index="1">
                            <template #title>
                                <el-icon>
                                    <location />
                                </el-icon>
                                <span>权限管理</span>
                            </template>
                            <el-menu-item index="/GetUserList">用户管理</el-menu-item>
                            <el-menu-item index="/GetRoleList">角色管理</el-menu-item>
                            <el-menu-item index="/GetPermissionList">权限管理</el-menu-item>
                            {{ Menulist }}
                            <el-menu-item v-for="(item, index) in Menulist" :index="index">{{ item
                                }}</el-menu-item>{{ Menulist }}
                        </el-sub-menu>
                    </el-menu>
                </el-aside>
                <el-main class="app-main">
                    <RouterView />
                </el-main>
            </el-container>
        </el-container>
    </div>
</template>
<script lang="ts" setup>
import {
    Document,
    Menu as IconMenu,
    Location,
    Setting,
    User,
    ArrowDown,
    SwitchButton
} from '@element-plus/icons-vue'
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import axios from 'axios'

const router = useRouter()
const userName = ref('')
const roleName = ref('')

onMounted(() => {
    axios.defaults.headers.common["Authorization"] = "Bearer " + window.localStorage.getItem("token");

    userName.value = localStorage.getItem('userName') || ''
    roleName.value = localStorage.getItem('roleName') || ''
    getMenu();
})

const handleOpen = (key: string, keyPath: string[]) => {
    console.log(key, keyPath)
}
const handleClose = (key: string, keyPath: string[]) => {
    console.log(key, keyPath)
}
const Menulist = ref([]);
const getMenu = () => {
    axios({
        url: 'http://localhost:5043/api/RBAC/GetRoleList',
        method: 'get',
        params: {
            RoleName: roleName.value
        }
    })
        .then(res => {
            if (res.data.code == 200) {
                Menulist.value = res.data.pageDatas.permissionNames
                console.log(Menulist);

            }
        })
}
const logout = () => {
    localStorage.removeItem('userName')
    localStorage.removeItem('roleId')
    localStorage.removeItem('roleName')
    localStorage.removeItem('token')
    ElMessage.success('退出登录成功')
    router.push('/')
}
</script>

<style scoped>
.main-container {
    min-height: 100vh;
    background: linear-gradient(135deg, #f5f7fa 0%, #e4ebf5 100%);
}

.app-header {
    background: linear-gradient(90deg, #4b6cb7 0%, #6c8bef 100%);
    color: white;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-weight: bold;
    font-size: 1.2rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    padding: 0 20px;
}

.user-info {
    display: flex;
    align-items: center;
}

.user-dropdown-link {
    display: flex;
    align-items: center;
    cursor: pointer;
    padding: 5px 10px;
    border-radius: 4px;
    transition: background-color 0.3s;
}

.user-dropdown-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.username {
    margin: 0 8px;
    font-size: 14px;
}

.user-avatar {
    background-color: #fff;
    color: #4b6cb7;
}

.el-menu-vertical-demo {
    border-right: none;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    height: 100%;
}

.app-main {
    padding: 20px;
}

.el-menu-item:hover {
    background-color: #e8f0fe !important;
}
</style>