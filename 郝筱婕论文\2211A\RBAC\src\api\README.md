# API 请求路径封装

## 项目结构

```
api/
├── config.ts         # 环境配置
├── index.ts          # API路径常量定义
├── request.ts        # Axios封装
├── services/         # API服务
│   ├── index.ts      # 服务导出
│   ├── user.ts       # 用户相关API
│   ├── role.ts       # 角色相关API
│   └── permission.ts # 权限相关API
└── README.md         # 说明文档
```

## 两个 API 路径说明

本项目使用了两个不同的 API 基础路径：

1. `http://localhost:5043/api` - 主要用于获取数据的接口

   - 登录
   - 获取用户列表
   - 获取角色列表
   - 获取权限列表

2. `http://localhost:5165/api` - 主要用于数据管理的接口
   - 创建用户/角色/权限
   - 更新用户/角色/权限
   - 删除用户/角色/权限

## 使用方法

### 1. 在组件中导入 API 服务

```typescript
// 导入单个服务模块
import { userApi } from "@/api/services";

// 或导入多个服务模块
import { userApi, roleApi, permissionApi } from "@/api/services";
```

### 2. 调用 API 方法

```typescript
// 用户登录 (使用5043端口)
userApi
  .login({
    UserName: "admin",
    Password: "123456",
  })
  .then((res) => {
    if (res.code === 200) {
      // 登录成功处理逻辑
    } else {
      // 登录失败处理逻辑
    }
  });

// 获取用户列表 (使用5043端口)
userApi
  .getUserList({
    PageIndex: 1,
    PageSize: 10,
    UserName: "",
  })
  .then((res) => {
    if (res.code === 200) {
      // 处理返回的用户列表
    }
  });

// 创建新用户 (使用5165端口)
userApi
  .createUser({
    userName: "newUser",
    password: "password",
    isEnable: true,
    roleId: [1],
  })
  .then((res) => {
    if (res.code === 200) {
      // 创建成功处理逻辑
    }
  });
```

### 3. 添加新的 API

1. 在 `index.ts` 中添加新的 API 路径常量，并根据端口分配到对应的对象中
2. 在相应的服务文件中添加新的 API 方法，并使用对应的 service 实例
3. 在组件中导入并使用

## 扩展说明

- 两个不同的 axios 请求实例已配置好：
  - `service1`: 对应 5043 端口
  - `service2`: 对应 5165 端口
- 每个请求实例都配置了请求拦截器自动添加 token
- 响应拦截器自动处理响应数据，直接返回 `response.data`
- 所有 API 已按功能模块分类并分配到对应的请求实例
