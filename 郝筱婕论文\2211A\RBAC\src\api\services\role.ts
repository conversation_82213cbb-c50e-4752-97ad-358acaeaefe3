import { service1, service2 } from '../request';
import API from '../index';

// 获取角色列表(使用service1 - 5043端口)
export function getRoleList(params?: {
  PageIndex?: number;
  PageSize?: number;
  UserName?: string;
}) {
  return service1({
    url: API.Role.getRoleList,
    method: 'get',
    params
  });
}

// 创建角色(使用service2 - 5165端口)
export function createRole(data: {
  roleName: string;
  isEnable: boolean;
  permissionIds: number[];
}) {
  return service2({
    url: API.RoleManage.createRole,
    method: 'post',
    data
  });
}

// 更新角色(使用service2 - 5165端口)
export function updateRole(data: {
  id: number | string;
  roleName: string;
  isEnable: boolean;
  permissionIds: number[];
}) {
  return service2({
    url: API.RoleManage.updateRole,
    method: 'put',
    data
  });
}

// 删除角色(使用service2 - 5165端口)
export function deleteRole(data: {
  id: number | string;
}) {
  return service2({
    url: API.RoleManage.deleteRole,
    method: 'delete',
    data
  });
} 