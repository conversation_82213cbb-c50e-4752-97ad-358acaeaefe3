/* RBAC Theme - Light Gradient Style */

/* Common container styles */
.page-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #e4ebf5 100%);
  border: none !important;
  box-shadow: none !important;
}

/* Header styles */
.app-header {
  background: linear-gradient(90deg, #4b6cb7 0%, #6c8bef 100%);
  color: white;
  display: flex;
  align-items: center;
  font-weight: bold;
  font-size: 1.2rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Card styles */
.el-card {
  border-radius: 8px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.05) !important;
  margin-bottom: 16px;
  border: none !important;
  overflow: hidden;
}

.el-card.search-card,
.el-card.table-card {
  background-color: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
}

.el-card__header {
  background-color: #f9fafc;
  border-bottom: 1px solid #ebeef5;
  padding: 12px 20px;
}

/* Breadcrumb styles */
.breadcrumb-container {
  padding: 8px 0;
}

/* Form styles */
.el-form-item {
  margin-bottom: 18px;
}

.el-input__inner,
.el-select__inner,
.el-button {
  border-radius: 6px;
}

.el-input__inner:focus,
.el-textarea__inner:focus {
  border-color: #6c8bef;
}

/* Button styles */
.el-button--primary {
  background: linear-gradient(90deg, #4b6cb7 0%, #6c8bef 100%);
  border-color: #4b6cb7;
}

.el-button--success {
  background: linear-gradient(90deg, #28a745 0%, #34d058 100%);
  border-color: #28a745;
}

.el-button--danger {
  background: linear-gradient(90deg, #dc3545 0%, #f55a4e 100%);
  border-color: #dc3545;
}

/* Table styles */
.el-table {
  border-radius: 8px;
  overflow: hidden;
}

.el-table th {
  background-color: #f5f7fa;
  font-weight: 600;
  color: #2c3e50;
}

.el-table--striped .el-table__row--striped td {
  background: #f9fafc;
}

.el-table--enable-row-hover .el-table__row:hover td {
  background-color: #e8f0fe !important;
}

/* Pagination styles */
.pagination-container {
  display: flex;
  justify-content: flex-end;
  margin-top: 16px;
  padding: 10px 0;
}

/* Dialog styles */
.el-dialog {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.el-dialog__header {
  background: linear-gradient(90deg, #4b6cb7 0%, #6c8bef 100%);
  color: white;
  padding: 16px 20px;
}

.el-dialog__title {
  color: white;
  font-weight: 600;
}

.el-dialog__body {
  padding: 24px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

/* Permission container for checkboxes */
.permission-container {
  max-height: 300px;
  overflow-y: auto;
  padding: 8px;
  border: 1px solid #dcdfe6;
  border-radius: 6px;
}

.permission-container .el-checkbox {
  margin-right: 15px;
  margin-bottom: 10px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .el-form--inline .el-form-item {
    margin-right: 0;
    width: 100%;
  }
}

/* Login page specific */
.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #e4ebf5 100%);
}

.login-card {
  width: 400px;
  border-radius: 12px;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
  background-color: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
}

.login-header {
  text-align: center;
  margin-bottom: 24px;
  font-size: 24px;
  font-weight: bold;
  color: #4b6cb7;
}

/* Custom shadow for cards */
.shadow-hover:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 24px rgba(107, 139, 239, 0.2) !important;
  transition: all 0.3s ease;
} 