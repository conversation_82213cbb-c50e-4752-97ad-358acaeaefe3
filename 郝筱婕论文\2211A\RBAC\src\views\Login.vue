<template>
    <div class="login-container">
        <el-card class="login-card" shadow="hover">
            <div class="login-header">
                <h2>RBAC 系统登录</h2>
            </div>
            <el-form ref="ruleFormRef" :model="ruleForm" :rules="rules" label-position="top">
                <el-form-item label="用户名" prop="UserName">
                    <el-input v-model="ruleForm.UserName" prefix-icon="User" placeholder="请输入用户名" />
                </el-form-item>
                <el-form-item label="密码" prop="Password">
                    <el-input v-model="ruleForm.Password" type="password" prefix-icon="Lock" show-password
                        placeholder="请输入密码" />
                </el-form-item>
                <el-form-item label="验证码" prop="captcha">
                    <div class="captcha-container">
                        <el-input v-model="ruleForm.captcha" placeholder="请输入验证码" />
                        <div class="captcha-box" @click="refreshCaptcha">{{ captcha }}</div>
                    </div>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" style="width: 100%" @click="submitForm(ruleFormRef)">
                        登录
                    </el-button>
                </el-form-item>
                <el-form-item>
                    <el-button style="width: 100%" @click="resetForm(ruleFormRef)">重置</el-button>
                </el-form-item>
            </el-form>
        </el-card>
    </div>
</template>

<script lang="ts" setup>
import { reactive, ref, onMounted } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { User, Lock } from '@element-plus/icons-vue'
import router from '@/router'
import { userApi } from '@/api/services'

const ruleFormRef = ref<FormInstance>()
const ruleForm = reactive({
    UserName: '',
    Password: '',
    captcha: '',
})

const captcha = ref('')

// 生成四位字母数字随机验证码
const generateCaptcha = () => {
    const characters = 'ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnpqrstuvwxyz23456789'
    let result = ''
    for (let i = 0; i < 4; i++) {
        result += characters.charAt(Math.floor(Math.random() * characters.length))
    }
    captcha.value = result
}

// 刷新验证码
const refreshCaptcha = () => {
    generateCaptcha()
}

// 页面加载时生成验证码
onMounted(() => {
    generateCaptcha()
})

const rules = reactive<FormRules>({
    UserName: [
        { required: true, message: '用户名不能为空', trigger: 'blur' },
    ],
    Password: [
        {
            required: true,
            message: '密码不能为空',
            trigger: 'change',
        },
    ],
    captcha: [
        {
            required: true,
            message: '验证码不能为空',
            trigger: 'blur',
        },
    ],
})

const submitForm = async (formEl: FormInstance | undefined) => {
    if (!formEl) return
    await formEl.validate((valid, fields) => {
        if (valid) {
            // 验证码校验
            if (ruleForm.captcha.toLowerCase() !== captcha.value.toLowerCase()) {
                ElMessage.error('验证码错误')
                return
            }

            userApi.login(ruleForm)
                .then((res: any) => {
                    if (res.code == 200) {
                        ElMessage.success(res.msg);
                        localStorage.setItem("userName", res.data.userName);
                        localStorage.setItem("roleId", res.data.roleId);
                        localStorage.setItem("roleName", res.data.roleName);
                        localStorage.setItem("token", res.data.token);
                        router.push('/Menu');
                    }
                    else {
                        ElMessage.error(res.msg);
                        return;
                    }
                })
        } else {
            console.log('error submit!', fields)
        }
    })
}

const resetForm = (formEl: FormInstance | undefined) => {
    if (!formEl) return
    formEl.resetFields()
    generateCaptcha()
}
</script>

<style scoped>
.login-container {
    height: 100vh;
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    background: linear-gradient(135deg, #f5f7fa 0%, #e4ebf5 100%);
}

.login-card {
    width: 400px;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    background-color: #ffffff;
}

.login-header {
    margin-bottom: 30px;
    text-align: center;
}

.login-header h2 {
    color: #4b6cb7;
    margin: 0;
    font-size: 28px;
}

.captcha-container {
    display: flex;
    align-items: center;
}

.captcha-box {
    min-width: 100px;
    height: 40px;
    margin-left: 10px;
    background-color: #f2f6fc;
    display: flex;
    justify-content: center;
    align-items: center;
    font-family: 'Courier New', monospace;
    font-weight: bold;
    font-size: 18px;
    letter-spacing: 3px;
    color: #409EFF;
    cursor: pointer;
    user-select: none;
    border-radius: 4px;
}
</style>
