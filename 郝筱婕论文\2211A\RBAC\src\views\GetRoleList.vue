<template>
    <el-card class="page-container" shadow="hover">
        <template #header>
            <div class="breadcrumb-container">
                <el-breadcrumb separator="/">
                    <el-breadcrumb-item :to="{ path: '/' }">首页</el-breadcrumb-item>
                    <el-breadcrumb-item>
                        <a href="/Menu">RBAC</a>
                    </el-breadcrumb-item>
                    <el-breadcrumb-item>角色管理</el-breadcrumb-item>
                </el-breadcrumb>
            </div>
        </template>

        <el-card class="search-card" shadow="never">
            <el-form :inline="true">
                <el-form-item>
                    <el-input v-model="getuser.RoleName" prefix-icon="Search" style="width: 220px;"
                        placeholder="请输入角色名称"></el-input>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" :icon="Search" @click="gettableData()">查询</el-button>
                </el-form-item>
                <el-form-item>
                    <el-button type="success" :icon="Plus" @click="dialogVisible = true">新增角色</el-button>
                </el-form-item>
            </el-form>
        </el-card>

        <el-card class="table-card" shadow="never">
            <el-table :data="tableData" style="width: 100%" border stripe highlight-current-row
                v-loading="tableLoading">
                <el-table-column type="index" label="序号" width="80" align="center" />
                <el-table-column prop="id" label="角色编号" width="100" align="center" />
                <el-table-column prop="roleName" label="角色名称" width="150" />
                <el-table-column prop="isEnable" label="状态" width="100" align="center">
                    <template #default="scope">
                        <el-switch v-model="scope.row.isEnable" inline-prompt :active-text="'启用'" :inactive-text="'停用'"
                            @change="() => updateenable(scope.row)" />
                    </template>
                </el-table-column>
                <el-table-column prop="permissionNames" label="权限名称" min-width="200" show-overflow-tooltip />
                <el-table-column fixed="right" label="操作" width="150" align="center">
                    <template #default="scope">
                        <el-button type="primary" size="small" :icon="Edit" circle @click="toUpdate(scope.row)" />
                        <el-button type="danger" size="small" :icon="Delete" circle
                            @click="handleClick(scope.row.id)" />
                    </template>
                </el-table-column>
            </el-table>

            <div class="pagination-container">
                <el-pagination v-model:current-page="Page.PageIndex" v-model:page-size="Page.PageSize"
                    :page-sizes="[5, 10, 20, 50]" :background="true" layout="total, sizes, prev, pager, next, jumper"
                    :total="Page.totalCount" />
            </div>
        </el-card>
    </el-card>

    <!-- 新增角色对话框 -->
    <el-dialog v-model="dialogVisible" title="新增角色" width="500" destroy-on-close center>
        <el-form :model="form" label-position="top" style="max-width: 460px; margin: 0 auto">
            <el-form-item label="角色名称" required>
                <el-input v-model="form.roleName" placeholder="请输入角色名称" />
            </el-form-item>
            <el-form-item label="角色权限" required>
                <div class="permission-container">
                    <el-checkbox-group v-model="form.permissionIds">
                        <el-checkbox v-for="item in data" :key="item.id" :value="item.id" border>
                            {{ item.permissionName }}
                        </el-checkbox>
                    </el-checkbox-group>
                </div>
            </el-form-item>
            <el-form-item>
                <div class="dialog-footer">
                    <el-button @click="dialogVisible = false">取消</el-button>
                    <el-button type="primary" @click="onSubmit" :loading="submitLoading">添加角色</el-button>
                </div>
            </el-form-item>
        </el-form>
    </el-dialog>

    <!-- 编辑角色对话框 -->
    <el-dialog v-model="UpdatedialogVisible" title="编辑角色" width="500" destroy-on-close center>
        <el-form :model="Updateform" label-position="top" style="max-width: 460px; margin: 0 auto">
            <el-form-item label="角色名称" required>
                <el-input v-model="Updateform.roleName" placeholder="请输入角色名称" />
            </el-form-item>
            <el-form-item label="角色权限" required>
                <div class="permission-container">
                    <el-checkbox-group v-model="Updateform.permissionIds">
                        <el-checkbox v-for="item in data" :key="item.id" :value="item.id" border>
                            {{ item.permissionName }}
                        </el-checkbox>
                    </el-checkbox-group>
                </div>
            </el-form-item>
            <el-form-item>
                <div class="dialog-footer">
                    <el-button @click="UpdatedialogVisible = false">取消</el-button>
                    <el-button type="primary" @click="Update" :loading="updateLoading">保存修改</el-button>
                </div>
            </el-form-item>
        </el-form>
    </el-dialog>
</template>

<script lang="ts" setup>
import axios from 'axios'
import { ElMessage, ElMessageBox } from 'element-plus'
import { onMounted, reactive, ref, watch } from 'vue'
import { Search, Plus, Edit, Delete } from '@element-plus/icons-vue'

onMounted(() => {
    axios.defaults.headers.common["Authorization"] = "Bearer " + window.localStorage.getItem("token");
    gettableData();
    getdata();
})

// 加载状态
const tableLoading = ref(false);
const submitLoading = ref(false);
const updateLoading = ref(false);

const dialogVisible = ref(false)

interface FormType {
    roleName: string;
    isEnable: boolean;
    permissionIds: number[];
}

const form = reactive<FormType>({
    "roleName": "",
    "isEnable": true,
    "permissionIds": []
})

const onSubmit = () => {
    if (!form.roleName) {
        ElMessage.warning('请输入角色名称');
        return;
    }

    submitLoading.value = true;
    axios({
        url: 'http://localhost:5165/api/RBAC/CreateRoleC',
        method: 'post',
        data: form
    })
        .then(res => {
            if (res.data.code == 200) {
                ElMessage.success(res.data.msg);
                gettableData();
                dialogVisible.value = false;
                // 重置表单
                form.roleName = "";
                form.permissionIds = [];
            }
            else {
                ElMessage.error(res.data.msg);
            }
        })
        .catch(err => {
            ElMessage.error('创建角色失败: ' + err.message);
        })
        .finally(() => {
            submitLoading.value = false;
        });
}

const handleClick = (id: any) => {
    ElMessageBox.confirm(
        '确定要删除该角色吗？此操作不可恢复。',
        '删除确认',
        {
            confirmButtonText: '确定删除',
            cancelButtonText: '取消',
            type: 'warning',
            draggable: true,
        }
    )
        .then(() => {
            tableLoading.value = true;
            axios({
                url: 'http://localhost:5165/api/RBAC/DeleteRole',
                method: 'delete',
                data: {
                    id: id
                }
            })
                .then(res => {
                    if (res.data.code == 200) {
                        ElMessage.success(res.data.msg);
                        gettableData();
                    }
                    else {
                        ElMessage.error(res.data.msg);
                    }
                })
                .catch(err => {
                    ElMessage.error('删除角色失败: ' + err.message);
                })
                .finally(() => {
                    tableLoading.value = false;
                });
        })
        .catch(() => {
            ElMessage({
                type: 'info',
                message: '已取消删除操作',
            })
        });
}

const UpdatedialogVisible = ref(false);

interface UpdateFormType {
    id: number;
    roleName: string;
    isEnable: boolean;
    permissionIds: number[];
}

const Updateform = reactive<UpdateFormType>({
    "id": 0,
    "roleName": "",
    "isEnable": true,
    "permissionIds": []
})

interface PermissionItem {
    id: number;
    permissionName: string;
}

interface RoleItem {
    id: number;
    roleName: string;
    isEnable: boolean;
    permissionIds?: number[];
    permissionNames?: string;
}

const tableData = ref<RoleItem[]>([])
const Page = reactive({
    PageIndex: 1,
    PageSize: 5,
    totalCount: 0
})

const getuser: any = reactive({
    RoleName: ''
})

const gettableData = () => {
    tableLoading.value = true;
    axios({
        url: 'http://localhost:5043/api/RBAC/GetRoleList',
        method: 'get',
        params: {
            PageIndex: Page.PageIndex,
            PageSize: Page.PageSize,
            UserName: getuser.UserName
        }
    })
        .then(res => {
            if (res.data.code == 200) {
                tableData.value = res.data.pageDatas || [];
                Page.totalCount = res.data.totalCount || 0;
            } else {
                ElMessage.error(res.data.msg || '获取角色列表失败');
                tableData.value = [];
                Page.totalCount = 0;
            }
        })
        .catch(err => {
            ElMessage.error('获取角色列表失败: ' + err.message);
            tableData.value = [];
            Page.totalCount = 0;
        })
        .finally(() => {
            tableLoading.value = false;
        });
}

const data = ref<PermissionItem[]>([])
const getdata = () => {
    axios({
        url: 'http://localhost:5043/api/RBAC/GetPermissionList',
        method: 'get',
    })
        .then(res => {
            if (res.data.code == 200) {
                data.value = res.data.pageDatas || [];
            } else {
                ElMessage.error(res.data.msg || '获取权限列表失败');
                data.value = [];
            }
        })
        .catch(err => {
            ElMessage.error('获取权限列表失败: ' + err.message);
            data.value = [];
        });
}

watch(Page, () => {
    gettableData();
})

const updateenable = (row: any) => {
    axios({
        url: 'http://localhost:5165/api/RBAC/UpdateRoleEnable',
        method: 'put',
        data: {
            id: row.id,
            isEnable: row.isEnable
        }
    })
        .then(res => {
            if (res.data.code == 200) {
                ElMessage.success(res.data.msg);
                gettableData();
            }
            else {
                ElMessage.error(res.data.msg);
                // 恢复原值
                row.isEnable = !row.isEnable;
            }
        })
        .catch(err => {
            ElMessage.error('更新状态失败: ' + err.message);
            // 恢复原值
            row.isEnable = !row.isEnable;
        });
}

const toUpdate = (row: RoleItem) => {
    UpdatedialogVisible.value = true;
    Updateform.id = row.id;
    Updateform.roleName = row.roleName;
    Updateform.isEnable = row.isEnable;
    Updateform.permissionIds = Array.isArray(row.permissionIds) ? [...row.permissionIds] : [];
    console.log('编辑角色:', row);
}

const Update = () => {
    if (!Updateform.roleName) {
        ElMessage.warning('请输入角色名称');
        return;
    }

    updateLoading.value = true;
    axios({
        url: 'http://localhost:5165/api/RBAC/UpdateRole',
        method: 'put',
        data: Updateform
    })
        .then(res => {
            if (res.data.code == 200) {
                ElMessage.success(res.data.msg);
                gettableData();
                UpdatedialogVisible.value = false;
            }
            else {
                ElMessage.error(res.data.msg);
            }
        })
        .catch(err => {
            ElMessage.error('更新角色失败: ' + err.message);
        })
        .finally(() => {
            updateLoading.value = false;
        });
}
</script>

<style scoped>
.page-container {
    margin-bottom: 20px;
}

.breadcrumb-container {
    display: flex;
    align-items: center;
    font-size: 16px;
}

.search-card {
    margin-bottom: 20px;
    border-radius: 4px;
}

.table-card {
    border-radius: 4px;
}

.pagination-container {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
}

.permission-container {
    max-height: 300px;
    overflow-y: auto;
    padding: 10px;
    border: 1px solid #ebeef5;
    border-radius: 4px;
}

.permission-container .el-checkbox {
    margin-right: 15px;
    margin-bottom: 10px;
}

.dialog-footer {
    display: flex;
    justify-content: flex-end;
    margin-top: 20px;
    gap: 10px;
}
</style>
