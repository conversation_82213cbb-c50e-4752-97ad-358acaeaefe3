import { createRouter, createWebHistory } from 'vue-router'
import HomeView from '../views/HomeView.vue'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/HomeView',
      name: 'home',
      component: HomeView,
    },
    {
      path: '/about',
      name: 'about',
      // route level code-splitting
      // this generates a separate chunk (About.[hash].js) for this route
      // which is lazy-loaded when the route is visited.
      component: () => import('../views/AboutView.vue'),
    },
    {
      path:'/',
      component:()=>import('@/views/Login.vue')
    },
    {
      path:'/Menu',
      component:()=>import('@/views/Menu.vue'),
      children:[
        {
          path:'/GetUserList',
          component:()=>import('@/views/GetUserList.vue')
        },
        {
          path:'/GetRoleList',
          component:()=>import('@/views/GetRoleList.vue')
        },
        {
          path:'/GetPermissionList',
          component:()=>import('@/views/GetPermissionList.vue')
        }
      ]
    }
  ],
})

export default router
