<script setup lang="ts">
</script>

<template>
  <el-card class="page-container" shadow="hover">
    <div class="home-container">
      <div class="welcome-header">
        <h1>RBAC 用户角色权限管理系统</h1>
        <h3>欢迎使用本系统</h3>
      </div>
      <div class="features-container">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-card class="feature-card shadow-hover">
              <template #header>
                <div class="feature-header">
                  <el-icon>
                    <User />
                  </el-icon>
                  <h2>用户管理</h2>
                </div>
              </template>
              <p>系统用户的增删改查操作，以及用户角色分配</p>
              <el-button type="primary" @click="$router.push('/GetUserList')">查看用户</el-button>
            </el-card>
          </el-col>
          <el-col :span="8">
            <el-card class="feature-card shadow-hover">
              <template #header>
                <div class="feature-header">
                  <el-icon>
                    <UserFilled />
                  </el-icon>
                  <h2>角色管理</h2>
                </div>
              </template>
              <p>系统角色的增删改查操作，以及角色权限分配</p>
              <el-button type="primary" @click="$router.push('/GetRoleList')">查看角色</el-button>
            </el-card>
          </el-col>
          <el-col :span="8">
            <el-card class="feature-card shadow-hover">
              <template #header>
                <div class="feature-header">
                  <el-icon>
                    <Lock />
                  </el-icon>
                  <h2>权限管理</h2>
                </div>
              </template>
              <p>系统权限的增删改查操作，以及资源权限配置</p>
              <el-button type="primary" @click="$router.push('/GetPermissionList')">查看权限</el-button>
            </el-card>
          </el-col>
        </el-row>
      </div>
    </div>
  </el-card>
</template>

<script lang="ts">
import { User, UserFilled, Lock } from '@element-plus/icons-vue'
export default {
  name: 'HomeView',
  // 组件逻辑...
};
</script>

<style scoped>
.home-container {
  padding: 20px;
}

.welcome-header {
  text-align: center;
  margin-bottom: 40px;
}

.welcome-header h1 {
  font-size: 32px;
  color: #4b6cb7;
  margin-bottom: 10px;
}

.welcome-header h3 {
  font-size: 18px;
  color: #606266;
  font-weight: normal;
}

.features-container {
  margin-top: 30px;
}

.feature-card {
  height: 220px;
  display: flex;
  flex-direction: column;
  transition: all 0.3s ease;
}

.feature-header {
  display: flex;
  align-items: center;
  gap: 10px;
}

.feature-header h2 {
  margin: 0;
  font-size: 20px;
  color: #4b6cb7;
}

.feature-card p {
  margin: 20px 0;
  flex-grow: 1;
  color: #606266;
}

.el-icon {
  font-size: 24px;
  color: #4b6cb7;
}
</style>
