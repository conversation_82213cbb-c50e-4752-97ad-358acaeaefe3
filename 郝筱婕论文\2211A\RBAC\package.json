{"name": "rbac", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite --mode development", "test": "vite --mode test", "test-build": "vite build --mode test", "build": "vite build --mode production", "preview": "vite preview"}, "dependencies": {"axios": "^1.10.0", "element-plus": "^2.10.2", "pinia": "^3.0.1", "vue": "^3.5.13", "vue-router": "^4.5.0"}, "devDependencies": {"@tsconfig/node22": "^22.0.1", "@types/node": "^22.16.2", "@vitejs/plugin-vue": "^5.2.3", "@vue/compiler-sfc": "^3.5.17", "@vue/tsconfig": "^0.7.0", "npm-run-all2": "^7.0.2", "typescript": "~5.8.0", "vite": "^6.2.4", "vite-plugin-vue-devtools": "^7.7.2", "vue-tsc": "^2.2.8"}}