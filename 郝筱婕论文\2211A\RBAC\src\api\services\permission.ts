import { service1, service2 } from '../request';
import API from '../index';

// 获取权限列表(使用service1 - 5043端口)
export function getPermissionList() {
  return service1({
    url: API.Permission.getPermissionList,
    method: 'get'
  });
}

// 创建权限(使用service2 - 5165端口)
export function createPermission(data: {
  permissionName: string;
  permissionURL: string;
  parentId: number;
}) {
  return service2({
    url: API.PermissionManage.createPermission,
    method: 'post',
    data
  });
}

// 更新权限(使用service2 - 5165端口)
export function updatePermission(data: {
  id: number | string;
  permissionName: string;
  permissionURL: string;
  parentId: number;
}) {
  return service2({
    url: API.PermissionManage.updatePermission,
    method: 'put',
    data
  });
}

// 删除权限(使用service2 - 5165端口)
export function deletePermission(data: {
  id: number | string;
}) {
  return service2({
    url: API.PermissionManage.deletePermission,
    method: 'delete',
    data
  });
} 