<template>
    <el-card class="page-container" shadow="hover">
        <template #header>
            <div class="breadcrumb-container">
                <el-breadcrumb separator="/">
                    <el-breadcrumb-item :to="{ path: '/' }">首页</el-breadcrumb-item>
                    <el-breadcrumb-item>
                        <a href="/Menu">RBAC</a>
                    </el-breadcrumb-item>
                    <el-breadcrumb-item>用户管理</el-breadcrumb-item>
                </el-breadcrumb>
            </div>
        </template>

        <el-card class="search-card" shadow="never">
            <el-form :inline="true">
                <el-form-item>
                    <el-input v-model="getuser.UserName" prefix-icon="Search" style="width: 220px;"
                        placeholder="请输入用户名称"></el-input>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" :icon="Search" @click="gettableData()">查询</el-button>
                </el-form-item>
                <el-form-item>
                    <el-button type="success" :icon="Plus" @click="dialogVisible = true">新增用户</el-button>
                </el-form-item>
            </el-form>
        </el-card>

        <el-card class="table-card" shadow="never">
            <el-table :data="tableData" style="width: 100%" border stripe highlight-current-row
                v-loading="tableLoading">
                <el-table-column type="index" label="序号" width="80" align="center" />
                <el-table-column prop="id" label="用户编号" width="100" align="center" />
                <el-table-column prop="userName" label="用户名称" width="150" />
                <el-table-column prop="isEnable" label="状态" width="100" align="center">
                    <template #default="scope">
                        <el-switch v-model="scope.row.isEnable" inline-prompt :active-text="'启用'" :inactive-text="'停用'"
                            @change="() => updateenable(scope.row)" />
                    </template>
                </el-table-column>
                <el-table-column prop="roleName" label="角色名称" min-width="150" />
                <el-table-column fixed="right" label="操作" width="150" align="center">
                    <template #default="scope">
                        <el-button type="primary" size="small" :icon="Edit" circle @click="toUpdate(scope.row)" />
                        <el-button type="danger" size="small" :icon="Delete" circle
                            @click="handleClick(scope.row.id)" />
                    </template>
                </el-table-column>
            </el-table>

            <div class="pagination-container">
                <el-pagination v-model:current-page="Page.PageIndex" v-model:page-size="Page.PageSize"
                    :page-sizes="[5, 10, 20, 50]" :background="true" layout="total, sizes, prev, pager, next, jumper"
                    :total="Page.totalCount" />
            </div>
        </el-card>

        <!-- 新增用户对话框 -->
        <el-dialog v-model="dialogVisible" title="新增用户" width="500" destroy-on-close center>
            <el-form :model="form" label-position="top" style="max-width: 460px; margin: 0 auto">
                <el-form-item label="用户名称" required>
                    <el-input v-model="form.userName" placeholder="请输入用户名称" />
                </el-form-item>
                <el-form-item label="密码" required>
                    <el-input v-model="form.password" type="password" show-password placeholder="请输入密码" />
                </el-form-item>
                <el-form-item label="用户角色" required>
                    <div class="permission-container">
                        <el-checkbox-group v-model="form.roleId">
                            <el-checkbox v-for="item in data" :key="item.id" :value="item.id" border>
                                {{ item.roleName }}
                            </el-checkbox>
                        </el-checkbox-group>
                    </div>
                </el-form-item>
                <el-form-item>
                    <div class="dialog-footer">
                        <el-button @click="dialogVisible = false">取消</el-button>
                        <el-button type="primary" @click="onSubmit" :loading="submitLoading">添加用户</el-button>
                    </div>
                </el-form-item>
            </el-form>
        </el-dialog>

        <!-- 编辑用户对话框 -->
        <el-dialog v-model="UpdatedialogVisible" title="编辑用户" width="500" destroy-on-close center>
            <el-form :model="Updateform" label-position="top" style="max-width: 460px; margin: 0 auto">
                <el-form-item label="用户名称" required>
                    <el-input v-model="Updateform.userName" placeholder="请输入用户名称" />
                </el-form-item>
                <el-form-item label="密码" required>
                    <el-input v-model="Updateform.password" type="password" show-password placeholder="请输入密码" />
                </el-form-item>
                <el-form-item label="用户角色" required>
                    <div class="permission-container">
                        <el-checkbox-group v-model="Updateform.roleId">
                            <el-checkbox v-for="item in data" :key="item.id" :value="item.id" border>
                                {{ item.roleName }}
                            </el-checkbox>
                        </el-checkbox-group>
                    </div>
                </el-form-item>
                <el-form-item>
                    <div class="dialog-footer">
                        <el-button @click="UpdatedialogVisible = false">取消</el-button>
                        <el-button type="primary" @click="Update" :loading="updateLoading">保存修改</el-button>
                    </div>
                </el-form-item>
            </el-form>
        </el-dialog>
    </el-card>
</template>

<script lang="ts" setup>
import axios from 'axios'
import { ElMessage, ElMessageBox } from 'element-plus'
import { onMounted, reactive, ref, watch } from 'vue'
import { Search, Plus, Edit, Delete } from '@element-plus/icons-vue'

// Define interfaces for data types
interface RoleItem {
    id: number;
    roleName: string;
}

interface UserItem {
    id: number;
    userName: string;
    password: string;
    isEnable: boolean;
    roleId: number[];
    roleName?: string;
}

onMounted(() => {
    axios.defaults.headers.common["Authorization"] = "Bearer " + window.localStorage.getItem("token");
    gettableData()
    getdata();
})

// 加载状态
const tableLoading = ref(false);
const submitLoading = ref(false);
const updateLoading = ref(false);

const value1 = ref(true)
const value2 = ref(true)
const dialogVisible = ref(false)
const form = reactive({
    "userName": "",
    "password": "",
    "isEnable": true,
    "roleId": []
})

const onSubmit = () => {
    if (!form.userName || !form.password) {
        ElMessage.warning('请填写必填项');
        return;
    }

    submitLoading.value = true;
    axios({
        url: 'http://localhost:5165/api/RBAC/CreateUser',
        method: 'post',
        data: form
    })
        .then(res => {
            if (res.data.code == 200) {
                ElMessage.success(res.data.msg);
                gettableData();
                dialogVisible.value = false;
                // 重置表单
                form.userName = "";
                form.password = "";
                form.roleId = [];
            }
            else {
                ElMessage.error(res.data.msg);
            }
        })
        .catch(err => {
            ElMessage.error('创建用户失败: ' + err.message);
        })
        .finally(() => {
            submitLoading.value = false;
        });
}

const handleClick = (id: any) => {
    ElMessageBox.confirm(
        '确定要删除该用户吗？此操作不可恢复。',
        '删除确认',
        {
            confirmButtonText: '确定删除',
            cancelButtonText: '取消',
            type: 'warning',
            draggable: true,
        }
    )
        .then(() => {
            tableLoading.value = true;
            axios({
                url: 'http://localhost:5165/api/RBAC/DeleteUser',
                method: 'delete',
                data: {
                    id: id
                }
            })
                .then(res => {
                    if (res.data.code == 200) {
                        ElMessage.success(res.data.msg);
                        gettableData();
                    }
                    else {
                        ElMessage.error(res.data.msg);
                    }
                })
                .catch(err => {
                    ElMessage.error('删除用户失败: ' + err.message);
                })
                .finally(() => {
                    tableLoading.value = false;
                });
        })
        .catch(() => {
            ElMessage({
                type: 'info',
                message: '已取消删除操作',
            })
        });
}

const UpdatedialogVisible = ref(false);
const Updateform = reactive({
    "id": 0,
    "userName": "",
    "password": "",
    "isEnable": true,
    "roleId": []
})

const toUpdate = (row: any) => {
    UpdatedialogVisible.value = true;
    Updateform.id = row.id;
    Updateform.userName = row.userName;
    Updateform.password = row.password;
    Updateform.roleId = row.roleId
}

const Update = () => {
    if (!Updateform.userName) {
        ElMessage.warning('用户名不能为空');
        return;
    }

    updateLoading.value = true;
    axios({
        url: 'http://localhost:5165/api/RBAC/UpdateUser',
        method: 'put',
        data: Updateform
    })
        .then(res => {
            if (res.data.code == 200) {
                ElMessage.success(res.data.msg);
                gettableData();
                UpdatedialogVisible.value = false;
            }
            else {
                ElMessage.error(res.data.msg);
            }
        })
        .catch(err => {
            ElMessage.error('更新用户失败: ' + err.message);
        })
        .finally(() => {
            updateLoading.value = false;
        });
}

const tableData = ref<UserItem[]>([])
const Page = reactive({
    PageIndex: 1,
    PageSize: 5,
    totalCount: 0
})

const getuser = reactive({
    UserName: ''
})

const gettableData = () => {
    tableLoading.value = true;
    axios({
        url: 'http://localhost:5043/api/RBAC/GetUserList',
        method: 'get',
        params: {
            PageIndex: Page.PageIndex,
            PageSize: Page.PageSize,
            UserName: getuser.UserName
        }
    })
        .then(res => {
            if (res.data.code == 200) {
                tableData.value = res.data.pageDatas;
                Page.totalCount = res.data.totalCount;
            }
        })
        .finally(() => {
            tableLoading.value = false;
        });
}

const data = ref<RoleItem[]>([])
const getdata = () => {
    axios({
        url: 'http://localhost:5043/api/RBAC/GetRoleList',
        method: 'get',
    })
        .then(res => {
            if (res.data.code == 200) {
                data.value = res.data.pageDatas;
            }
        })
}

watch(Page, () => {
    gettableData();
})

const updateenable = (row: any) => {
    const enable = ref(!row.isEnable);
    axios({
        url: 'http://localhost:5165/api/RBAC/UpdateUserEnable',
        method: 'put',
        data: {
            id: row.id,
            isEnable: row.isEnable
        }
    })
        .then(res => {
            if (res.data.code == 200) {
                ElMessage.success(res.data.msg);
                gettableData();
            }
            else {
                ElMessage.error(res.data.msg);
                return;
            }
        })
}
</script>
